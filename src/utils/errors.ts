/**
 * 错误处理工具类
 * 定义各种错误类型和错误处理函数
 */

import { Response } from 'express';
import { logger } from './logger';

/**
 * 应用程序基础错误类
 */
export class AppError extends Error {
  public readonly statusCode: number;
  public readonly code: string;
  public readonly isOperational: boolean;

  constructor(message: string, statusCode: number, code: string) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// 预定义的错误类型

/**
 * 验证错误 - 400
 */
export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400, 'VALIDATION_ERROR');
  }
}

/**
 * 认证错误 - 401
 */
export class AuthenticationError extends AppError {
  constructor(message: string = '认证失败') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

/**
 * 授权错误 - 403
 */
export class AuthorizationError extends AppError {
  constructor(message: string = '访问被拒绝') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

/**
 * 资源未找到错误 - 404
 */
export class NotFoundError extends AppError {
  constructor(message: string = '资源未找到') {
    super(message, 404, 'NOT_FOUND');
  }
}

/**
 * 冲突错误 - 409
 */
export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, 'CONFLICT');
  }
}

/**
 * 内部服务器错误 - 500
 */
export class InternalServerError extends AppError {
  constructor(message: string = '内部服务器错误') {
    super(message, 500, 'INTERNAL_SERVER_ERROR');
  }
}

// 游戏相关错误

/**
 * 游戏错误基类
 */
export class GameError extends AppError {
  constructor(message: string, code: string) {
    super(message, 400, code);
  }
}

/**
 * 房间已满错误
 */
export class RoomFullError extends GameError {
  constructor() {
    super('房间已满', 'ROOM_FULL');
  }
}

/**
 * 房间未找到错误
 */
export class RoomNotFoundError extends GameError {
  constructor() {
    super('房间未找到', 'ROOM_NOT_FOUND');
  }
}

/**
 * 玩家不在房间错误
 */
export class PlayerNotInRoomError extends GameError {
  constructor() {
    super('玩家不在此房间中', 'PLAYER_NOT_IN_ROOM');
  }
}

/**
 * 游戏未开始错误
 */
export class GameNotStartedError extends GameError {
  constructor() {
    super('游戏尚未开始', 'GAME_NOT_STARTED');
  }
}

/**
 * 无效游戏动作错误
 */
export class InvalidGameActionError extends GameError {
  constructor(message: string) {
    super(message, 'INVALID_GAME_ACTION');
  }
}

// Error response helper
export const sendErrorResponse = (
  res: Response,
  error: AppError | Error,
  details?: any
): void => {
  if (error instanceof AppError) {
    res.status(error.statusCode).json({
      error: {
        code: error.code,
        message: error.message,
        details,
      },
    });
  } else {
    // Unexpected error
    logger.error('Unexpected error:', error);
    res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred',
      },
    });
  }
};

// Global error handler middleware
export const globalErrorHandler = (
  error: Error,
  req: any,
  res: Response,
  next: any
): void => {
  logger.error('Global error handler:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    body: req.body,
    params: req.params,
    query: req.query,
  });

  if (error instanceof AppError) {
    sendErrorResponse(res, error);
  } else {
    // Handle specific error types
    if (error.name === 'ValidationError') {
      sendErrorResponse(res, new ValidationError(error.message));
    } else if (error.name === 'JsonWebTokenError') {
      sendErrorResponse(res, new AuthenticationError('Invalid token'));
    } else if (error.name === 'TokenExpiredError') {
      sendErrorResponse(res, new AuthenticationError('Token expired'));
    } else {
      sendErrorResponse(res, new InternalServerError());
    }
  }
};

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: any, res: Response, next: any) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Success response helper
export const sendSuccessResponse = (
  res: Response,
  data: any,
  message?: string,
  statusCode: number = 200
): void => {
  res.status(statusCode).json({
    success: true,
    message,
    data,
  });
};
