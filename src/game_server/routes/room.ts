import { Router, Request, Response } from 'express';
import { prisma } from '../../model/database';
import { redis } from '../../model/redis';
import {
  CreateRoomRequest,
  JoinRoomRequest,
  RoomInfo,
  RoomMemberInfo,
  RoomStatus,
  RoomMemberStatus,
  PaginationQuery,
} from '../../model/types';
import {
  createRoomSchema,
  joinRoomSchema,
  paginationSchema,
  validate,
} from '../../model/validators';
import {
  authenticateToken,
  AuthenticatedRequest,
} from '../../utils/auth';
import {
  asyncHandler,
  sendSuccessResponse,
  NotFoundError,
  ConflictError,
  ValidationError,
  RoomFullError,
  PlayerNotInRoomError,
} from '../../utils/errors';
import { logger } from '../../utils/logger';

const router = Router();

// Create room
router.post('/', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const roomData = validate<CreateRoomRequest>(createRoomSchema, req.body);

  // Check if user is already in a room
  const existingMembership = await prisma.roomMember.findFirst({
    where: {
      userId,
      status: { in: ['JOINED', 'READY', 'PLAYING'] },
    },
  });

  if (existingMembership) {
    throw new ConflictError('You are already in a room');
  }

  // Create room
  const room = await prisma.room.create({
    data: {
      name: roomData.name,
      gameType: roomData.gameType,
      maxPlayers: roomData.maxPlayers || 4,
      settings: roomData.settings,
    },
  });

  // Add creator as first member
  const roomMember = await prisma.roomMember.create({
    data: {
      userId,
      roomId: room.id,
      position: 0,
      status: 'JOINED',
    },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          nickname: true,
          avatar: true,
        },
      },
    },
  });

  // Create room info response
  const roomInfo: RoomInfo = {
    id: room.id,
    name: room.name,
    maxPlayers: room.maxPlayers,
    gameType: room.gameType,
    status: room.status,
    currentPlayers: 1,
    members: [{
      id: roomMember.id,
      userId: roomMember.userId,
      username: roomMember.user.username,
      nickname: roomMember.user.nickname,
      avatar: roomMember.user.avatar,
      position: roomMember.position,
      status: roomMember.status,
      joinedAt: roomMember.joinedAt,
    }],
    settings: room.settings,
  };

  logger.info(`Room created: ${room.name} by ${req.user!.username}`);
  sendSuccessResponse(res, roomInfo, 'Room created successfully', 201);
}));

// Join room
router.post('/:roomId/join', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const { roomId } = req.params;
  const joinData = validate<JoinRoomRequest>(joinRoomSchema, { roomId, ...req.body });

  // Check if room exists
  const room = await prisma.room.findUnique({
    where: { id: roomId },
    include: {
      members: {
        where: { status: { not: 'LEFT' } },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              nickname: true,
              avatar: true,
            },
          },
        },
      },
    },
  });

  if (!room) {
    throw new NotFoundError('Room not found');
  }

  // Check if room is full
  if (room.members.length >= room.maxPlayers) {
    throw new RoomFullError();
  }

  // Check if user is already in this room
  const existingMember = room.members.find(member => member.userId === userId);
  if (existingMember) {
    throw new ConflictError('You are already in this room');
  }

  // Check if user is in another room
  const existingMembership = await prisma.roomMember.findFirst({
    where: {
      userId,
      status: { in: ['JOINED', 'READY', 'PLAYING'] },
    },
  });

  if (existingMembership) {
    throw new ConflictError('You are already in another room');
  }

  // Find next available position
  const occupiedPositions = room.members.map(member => member.position);
  let position = 0;
  while (occupiedPositions.includes(position)) {
    position++;
  }

  // Add user to room
  const roomMember = await prisma.roomMember.create({
    data: {
      userId,
      roomId,
      position,
      status: 'JOINED',
    },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          nickname: true,
          avatar: true,
        },
      },
    },
  });

  // Get updated room info
  const updatedRoom = await prisma.room.findUnique({
    where: { id: roomId },
    include: {
      members: {
        where: { status: { not: 'LEFT' } },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              nickname: true,
              avatar: true,
            },
          },
        },
      },
    },
  });

  const roomInfo: RoomInfo = {
    id: updatedRoom!.id,
    name: updatedRoom!.name,
    maxPlayers: updatedRoom!.maxPlayers,
    gameType: updatedRoom!.gameType,
    status: updatedRoom!.status,
    currentPlayers: updatedRoom!.members.length,
    members: updatedRoom!.members.map(member => ({
      id: member.id,
      userId: member.userId,
      username: member.user.username,
      nickname: member.user.nickname,
      avatar: member.user.avatar,
      position: member.position,
      status: member.status,
      joinedAt: member.joinedAt,
    })),
    settings: updatedRoom!.settings,
  };

  logger.info(`User ${req.user!.username} joined room ${room.name}`);
  sendSuccessResponse(res, roomInfo, 'Joined room successfully');
}));

// Leave room
router.post('/:roomId/leave', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const { roomId } = req.params;

  // Find user's membership in the room
  const roomMember = await prisma.roomMember.findFirst({
    where: {
      userId,
      roomId,
      status: { not: 'LEFT' },
    },
  });

  if (!roomMember) {
    throw new PlayerNotInRoomError();
  }

  // Update member status to LEFT
  await prisma.roomMember.update({
    where: { id: roomMember.id },
    data: { status: 'LEFT' },
  });

  // Check if room is now empty
  const remainingMembers = await prisma.roomMember.count({
    where: {
      roomId,
      status: { not: 'LEFT' },
    },
  });

  // If room is empty, delete it
  if (remainingMembers === 0) {
    await prisma.room.delete({
      where: { id: roomId },
    });
    logger.info(`Room ${roomId} deleted (empty)`);
  }

  logger.info(`User ${req.user!.username} left room ${roomId}`);
  sendSuccessResponse(res, null, 'Left room successfully');
}));

// Get room info
router.get('/:roomId', asyncHandler(async (req, res) => {
  const { roomId } = req.params;

  const room = await prisma.room.findUnique({
    where: { id: roomId },
    include: {
      members: {
        where: { status: { not: 'LEFT' } },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              nickname: true,
              avatar: true,
            },
          },
        },
      },
    },
  });

  if (!room) {
    throw new NotFoundError('Room not found');
  }

  const roomInfo: RoomInfo = {
    id: room.id,
    name: room.name,
    maxPlayers: room.maxPlayers,
    gameType: room.gameType,
    status: room.status,
    currentPlayers: room.members.length,
    members: room.members.map(member => ({
      id: member.id,
      userId: member.userId,
      username: member.user.username,
      nickname: member.user.nickname,
      avatar: member.user.avatar,
      position: member.position,
      status: member.status,
      joinedAt: member.joinedAt,
    })),
    settings: room.settings,
  };

  sendSuccessResponse(res, roomInfo);
}));

// List rooms
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const { page = 1, limit = 10 } = validate<PaginationQuery>(paginationSchema, req.query);
  const { gameType, status } = req.query;

  const skip = (page - 1) * limit;

  const where: any = {};
  if (gameType) {
    where.gameType = gameType;
  }
  if (status && ['WAITING', 'PLAYING', 'FINISHED'].includes(status as string)) {
    where.status = status as RoomStatus;
  }

  const [rooms, total] = await Promise.all([
    prisma.room.findMany({
      where,
      include: {
        members: {
          where: { status: { not: 'LEFT' } },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                nickname: true,
                avatar: true,
              },
            },
          },
        },
      },
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
    }),
    prisma.room.count({ where }),
  ]);

  const roomList = rooms.map(room => ({
    id: room.id,
    name: room.name,
    maxPlayers: room.maxPlayers,
    gameType: room.gameType,
    status: room.status,
    currentPlayers: room.members.length,
    members: room.members.map(member => ({
      id: member.id,
      userId: member.userId,
      username: member.user.username,
      nickname: member.user.nickname,
      avatar: member.user.avatar,
      position: member.position,
      status: member.status,
      joinedAt: member.joinedAt,
    })),
    settings: room.settings,
  }));

  const response = {
    data: roomList,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  };

  sendSuccessResponse(res, response);
}));

export default router;
