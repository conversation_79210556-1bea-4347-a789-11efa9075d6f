import { Router } from 'express';
import { prisma } from '../../model/database';
import { redis } from '../../model/redis';
import {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  UserProfile,
} from '../../model/types';
import {
  registerSchema,
  loginSchema,
  validate,
} from '../../model/validators';
import {
  hashPassword,
  verifyPassword,
  generateToken,
  authenticateToken,
  AuthenticatedRequest,
} from '../../utils/auth';
import {
  asyncHandler,
  sendSuccessResponse,
  sendErrorResponse,
  ValidationError,
  AuthenticationError,
  ConflictError,
} from '../../utils/errors';
import { logger } from '../../utils/logger';

const router = Router();

// Register endpoint
router.post('/register', asyncHandler(async (req, res) => {
  const registerData = validate<RegisterRequest>(registerSchema, req.body);

  // Check if user already exists
  const existingUser = await prisma.user.findFirst({
    where: {
      OR: [
        { username: registerData.username },
        { email: registerData.email },
      ],
    },
  });

  if (existingUser) {
    if (existingUser.username === registerData.username) {
      throw new ConflictError('Username already exists');
    }
    if (existingUser.email === registerData.email) {
      throw new ConflictError('Email already exists');
    }
  }

  // Hash password
  const hashedPassword = await hashPassword(registerData.password);

  // Create user
  const user = await prisma.user.create({
    data: {
      username: registerData.username,
      email: registerData.email,
      password: hashedPassword,
      nickname: registerData.nickname || registerData.username,
    },
  });

  // Create user profile
  const userProfile: UserProfile = {
    id: user.id,
    username: user.username,
    nickname: user.nickname,
    avatar: user.avatar,
    level: user.level,
    experience: user.experience,
    coins: user.coins,
    createdAt: user.createdAt,
    lastLoginAt: user.lastLoginAt,
  };

  // Generate token
  const token = generateToken(userProfile);

  // Store session in Redis
  await redis.setSession(user.id, userProfile, 86400 * 7); // 7 days

  const response: AuthResponse = {
    user: userProfile,
    token,
    expiresIn: '7d',
  };

  logger.info(`User registered: ${user.username}`);
  sendSuccessResponse(res, response, 'User registered successfully', 201);
}));

// Login endpoint
router.post('/login', asyncHandler(async (req, res) => {
  const loginData = validate<LoginRequest>(loginSchema, req.body);

  // Find user
  const user = await prisma.user.findUnique({
    where: { username: loginData.username },
  });

  if (!user) {
    throw new AuthenticationError('Invalid username or password');
  }

  // Verify password
  const isPasswordValid = await verifyPassword(loginData.password, user.password);
  if (!isPasswordValid) {
    throw new AuthenticationError('Invalid username or password');
  }

  // Update last login time
  await prisma.user.update({
    where: { id: user.id },
    data: { lastLoginAt: new Date() },
  });

  // Create user profile
  const userProfile: UserProfile = {
    id: user.id,
    username: user.username,
    nickname: user.nickname,
    avatar: user.avatar,
    level: user.level,
    experience: user.experience,
    coins: user.coins,
    createdAt: user.createdAt,
    lastLoginAt: new Date(),
  };

  // Generate token
  const token = generateToken(userProfile);

  // Store session in Redis
  await redis.setSession(user.id, userProfile, 86400 * 7); // 7 days

  // Set user as online
  await redis.setUserOnline(user.id);

  const response: AuthResponse = {
    user: userProfile,
    token,
    expiresIn: '7d',
  };

  logger.info(`User logged in: ${user.username}`);
  sendSuccessResponse(res, response, 'Login successful');
}));

// Logout endpoint
router.post('/logout', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;

  // Remove session from Redis
  await redis.deleteSession(userId);

  // Set user as offline
  await redis.setUserOffline(userId);

  logger.info(`User logged out: ${req.user!.username}`);
  sendSuccessResponse(res, null, 'Logout successful');
}));

// Get current user endpoint
router.get('/me', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;

  // Get fresh user data from database
  const user = await prisma.user.findUnique({
    where: { id: userId },
  });

  if (!user) {
    throw new AuthenticationError('User not found');
  }

  const userProfile: UserProfile = {
    id: user.id,
    username: user.username,
    nickname: user.nickname,
    avatar: user.avatar,
    level: user.level,
    experience: user.experience,
    coins: user.coins,
    createdAt: user.createdAt,
    lastLoginAt: user.lastLoginAt,
  };

  sendSuccessResponse(res, userProfile);
}));

// Refresh token endpoint
router.post('/refresh', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;

  // Get user from database
  const user = await prisma.user.findUnique({
    where: { id: userId },
  });

  if (!user) {
    throw new AuthenticationError('User not found');
  }

  const userProfile: UserProfile = {
    id: user.id,
    username: user.username,
    nickname: user.nickname,
    avatar: user.avatar,
    level: user.level,
    experience: user.experience,
    coins: user.coins,
    createdAt: user.createdAt,
    lastLoginAt: user.lastLoginAt,
  };

  // Generate new token
  const token = generateToken(userProfile);

  // Update session in Redis
  await redis.setSession(user.id, userProfile, 86400 * 7); // 7 days

  const response: AuthResponse = {
    user: userProfile,
    token,
    expiresIn: '7d',
  };

  sendSuccessResponse(res, response, 'Token refreshed successfully');
}));

export default router;
