/**
 * 认证路由模块
 * 处理用户注册、登录、登出等认证相关功能
 */

import { Router } from 'express';
import { prisma } from '../../model/database';
import { redis } from '../../model/redis';
import {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  UserProfile,
} from '../../model/types';
import {
  registerSchema,
  loginSchema,
  validate,
} from '../../model/validators';
import {
  hashPassword,
  verifyPassword,
  generateToken,
  authenticateToken,
  AuthenticatedRequest,
} from '../../utils/auth';
import {
  asyncHandler,
  sendSuccessResponse,
  sendErrorResponse,
  ValidationError,
  AuthenticationError,
  ConflictError,
} from '../../utils/errors';
import { logger } from '../../utils/logger';

const router = Router();

/**
 * 用户注册接口
 * POST /api/auth/register
 */
router.post('/register', asyncHandler(async (req, res) => {
  // 验证请求数据
  const registerData = validate<RegisterRequest>(registerSchema, req.body);

  // 检查用户是否已存在
  const existingUser = await prisma.user.findFirst({
    where: {
      OR: [
        { username: registerData.username },
        { email: registerData.email },
      ],
    },
  });

  if (existingUser) {
    if (existingUser.username === registerData.username) {
      throw new ConflictError('用户名已存在');
    }
    if (existingUser.email === registerData.email) {
      throw new ConflictError('邮箱已存在');
    }
  }

  // 加密密码
  const hashedPassword = await hashPassword(registerData.password);

  // 创建用户
  const user = await prisma.user.create({
    data: {
      username: registerData.username,
      email: registerData.email,
      password: hashedPassword,
      nickname: registerData.nickname || registerData.username,
    },
  });

  // 构建用户资料
  const userProfile: UserProfile = {
    id: user.id,
    username: user.username,
    nickname: user.nickname,
    avatar: user.avatar,
    level: user.level,
    experience: user.experience,
    coins: user.coins,
    createdAt: user.createdAt,
    lastLoginAt: user.lastLoginAt,
  };

  // 生成JWT令牌
  const token = generateToken(userProfile);

  // 在Redis中存储会话信息，有效期7天
  await redis.setSession(user.id, userProfile, 86400 * 7);

  const response: AuthResponse = {
    user: userProfile,
    token,
    expiresIn: '7d',
  };

  logger.info(`用户注册成功: ${user.username}`);
  sendSuccessResponse(res, response, '用户注册成功', 201);
}));

/**
 * 用户登录接口
 * POST /api/auth/login
 */
router.post('/login', asyncHandler(async (req, res) => {
  // 验证登录数据
  const loginData = validate<LoginRequest>(loginSchema, req.body);

  // 查找用户
  const user = await prisma.user.findUnique({
    where: { username: loginData.username },
  });

  if (!user) {
    throw new AuthenticationError('用户名或密码错误');
  }

  // 验证密码
  const isPasswordValid = await verifyPassword(loginData.password, user.password);
  if (!isPasswordValid) {
    throw new AuthenticationError('用户名或密码错误');
  }

  // 更新最后登录时间
  await prisma.user.update({
    where: { id: user.id },
    data: { lastLoginAt: new Date() },
  });

  // 构建用户资料
  const userProfile: UserProfile = {
    id: user.id,
    username: user.username,
    nickname: user.nickname,
    avatar: user.avatar,
    level: user.level,
    experience: user.experience,
    coins: user.coins,
    createdAt: user.createdAt,
    lastLoginAt: new Date(),
  };

  // 生成JWT令牌
  const token = generateToken(userProfile);

  // 在Redis中存储会话信息，有效期7天
  await redis.setSession(user.id, userProfile, 86400 * 7);

  // 设置用户在线状态
  await redis.setUserOnline(user.id);

  const response: AuthResponse = {
    user: userProfile,
    token,
    expiresIn: '7d',
  };

  logger.info(`用户登录成功: ${user.username}`);
  sendSuccessResponse(res, response, '登录成功');
}));

/**
 * 用户登出接口
 * POST /api/auth/logout
 */
router.post('/logout', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;

  // 从Redis中删除会话信息
  await redis.deleteSession(userId);

  // 设置用户离线状态
  await redis.setUserOffline(userId);

  logger.info(`用户登出: ${req.user!.username}`);
  sendSuccessResponse(res, null, '登出成功');
}));

/**
 * 获取当前用户信息接口
 * GET /api/auth/me
 */
router.get('/me', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;

  // 从数据库获取最新的用户数据
  const user = await prisma.user.findUnique({
    where: { id: userId },
  });

  if (!user) {
    throw new AuthenticationError('用户不存在');
  }

  const userProfile: UserProfile = {
    id: user.id,
    username: user.username,
    nickname: user.nickname,
    avatar: user.avatar,
    level: user.level,
    experience: user.experience,
    coins: user.coins,
    createdAt: user.createdAt,
    lastLoginAt: user.lastLoginAt,
  };

  sendSuccessResponse(res, userProfile);
}));

/**
 * 刷新令牌接口
 * POST /api/auth/refresh
 */
router.post('/refresh', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;

  // 从数据库获取用户信息
  const user = await prisma.user.findUnique({
    where: { id: userId },
  });

  if (!user) {
    throw new AuthenticationError('用户不存在');
  }

  const userProfile: UserProfile = {
    id: user.id,
    username: user.username,
    nickname: user.nickname,
    avatar: user.avatar,
    level: user.level,
    experience: user.experience,
    coins: user.coins,
    createdAt: user.createdAt,
    lastLoginAt: user.lastLoginAt,
  };

  // 生成新的JWT令牌
  const token = generateToken(userProfile);

  // 更新Redis中的会话信息，有效期7天
  await redis.setSession(user.id, userProfile, 86400 * 7);

  const response: AuthResponse = {
    user: userProfile,
    token,
    expiresIn: '7d',
  };

  sendSuccessResponse(res, response, '令牌刷新成功');
}));

export default router;
