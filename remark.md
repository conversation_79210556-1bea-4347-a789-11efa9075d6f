# 项目概述

这是一个多人游戏服务器项目，包含以下核心模块：

* account_server: 账户管理服务器
* game_server: 游戏逻辑服务器
* model: 数据模型层

## 技术栈建议

* 运行时: Node.js + TypeScript
* Web框架: Express.js
* 数据库: mysql + Redis (缓存)
* WebSocket: Socket.io (实时通信)
* 认证: JWT
* ORM: Prisma 或 TypeORM

## 详细实施计划

1. 项目基础设施搭建

* 初始化 package.json 和 TypeScript 配置
* 设置开发环境和构建脚本
* 配置代码规范 (ESLint, Prettier)

2. 数据模型设计 (model/)

* 用户账户模型
* 游戏房间模型
* 游戏状态模型
* 游戏记录模型

3. 账户服务器 (account_server/)

* 用户注册/登录
* JWT 认证中间件
* 用户信息管理
* 好友系统

4. 游戏服务器 (game_server/)

* 房间管理系统
* 游戏逻辑引擎
* WebSocket 实时通信
* 游戏状态同步

5. 部署和运维

* Docker 容器化
* 数据库迁移脚本
* 监控和日志系统
